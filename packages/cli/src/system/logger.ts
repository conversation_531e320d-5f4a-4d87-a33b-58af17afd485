import type { LogLevel } from '@wcai/shared';

export class Logger {
  private static level: LogLevel = 'warn';

  static configure(level: LogLevel): void {
    this.level = level;
  }

  static log(message: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      console.error(`[INFO] ${message}`, ...args);
    }
  }

  static warn(message: string, ...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.error(`[WARN] ${message}`, ...args);
    }
  }

  static error(message: string, error?: any): void {
    if (this.shouldLog('error')) {
      console.error(`[ERROR] ${message}`, error);
    }
  }

  static debug(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.error(`[DEBUG] ${message}`, ...args);
    }
  }

  private static shouldLog(messageLevel: LogLevel): boolean {
    const levels: LogLevel[] = ['off', 'error', 'warn', 'info', 'debug'];
    const currentLevelIndex = levels.indexOf(this.level);
    const messageLevelIndex = levels.indexOf(messageLevel);
    
    return currentLevelIndex > 0 && messageLevelIndex <= currentLevelIndex;
  }
}
