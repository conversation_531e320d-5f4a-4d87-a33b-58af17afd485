import type { Component } from '@wc-toolkit/cem-utilities';
import { getAllComponents, getComponentByClassName, getComponentByTagName } from '@wc-toolkit/cem-utilities';
import type { Package } from 'custom-elements-manifest';
import { glob } from 'glob';
import * as fs from 'fs/promises';
import * as path from 'path';
import type { ComponentDetailLevel, SearchMatchingMode } from '@wcai/shared';
import { filterComponents, getComponentDetails } from '@wcai/shared';
import { Logger } from '../system/logger';

export class CemProvider {
  private manifests: Package[] = [];
  private components: Component[] = [];
  private lastScan = 0;
  private readonly scanInterval = 5000; // 5 seconds

  constructor(private workspace: string) {}

  async initialize(): Promise<void> {
    await this.scanManifests();
  }

  private async scanManifests(): Promise<void> {
    const now = Date.now();
    if (now - this.lastScan < this.scanInterval) {
      return; // Skip if scanned recently
    }

    try {
      Logger.log(`Scanning for custom-elements.json files in ${this.workspace}`);

      // Find all custom-elements.json files
      const manifestPaths = await glob('**/custom-elements.json', {
        cwd: this.workspace,
        ignore: ['**/node_modules/**', '**/dist/**', '**/build/**'],
        absolute: true,
      });

      // Also check node_modules for dependencies
      const nodeModulesPaths = await glob('node_modules/*/custom-elements.json', {
        cwd: this.workspace,
        absolute: true,
      });

      const allPaths = [...manifestPaths, ...nodeModulesPaths];
      Logger.log(`Found ${allPaths.length} manifest files`);

      // Load and parse manifests
      const manifests: Package[] = [];
      for (const manifestPath of allPaths) {
        try {
          const content = await fs.readFile(manifestPath, 'utf-8');
          const manifest = JSON.parse(content) as Package;
          manifests.push(manifest);
          Logger.log(`Loaded manifest: ${manifestPath}`);
        } catch (error) {
          Logger.error(`Failed to load manifest ${manifestPath}:`, error);
        }
      }

      this.manifests = manifests;

      // Extract all components
      this.components = [];
      for (const manifest of manifests) {
        const components = getAllComponents(manifest);
        this.components.push(...components);
      }

      Logger.log(`Loaded ${this.components.length} components from ${manifests.length} manifests`);
      this.lastScan = now;
    } catch (error) {
      Logger.error('Failed to scan manifests:', error);
    }
  }

  async getAllComponents(detail: ComponentDetailLevel = 'basic'): Promise<any[]> {
    await this.scanManifests();
    return this.components.map(component => getComponentDetails(component, detail));
  }

  async searchComponents(query: string, matching: SearchMatchingMode = 'any'): Promise<any[]> {
    await this.scanManifests();
    const filtered = filterComponents(this.components, query, matching);
    return filtered.map(component => getComponentDetails(component, 'public'));
  }

  async getComponentDetails(identifier: string, detail: ComponentDetailLevel = 'public'): Promise<any | null> {
    await this.scanManifests();

    // Try to find by tag name first
    for (const manifest of this.manifests) {
      const component = getComponentByTagName(manifest, identifier);
      if (component) {
        return getComponentDetails(component, detail);
      }
    }

    // Try to find by class name
    for (const manifest of this.manifests) {
      const component = getComponentByClassName(manifest, identifier);
      if (component) {
        return getComponentDetails(component, detail);
      }
    }

    return null;
  }

  getStats() {
    return {
      manifestCount: this.manifests.length,
      componentCount: this.components.length,
      lastScan: this.lastScan,
    };
  }
}
