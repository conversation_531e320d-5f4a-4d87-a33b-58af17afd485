import type { IncomingMessage, Server, ServerResponse } from 'http';
import { createServer } from 'http';
import type { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js';
import type { HttpTransportInfo, McpServerOptions } from '@wcai/shared';
import { uuid } from '@wcai/shared';
import { Logger } from '../system/logger';
import { createMcpServer } from '../mcp/server';

export async function createHttpTransport(
  port = 0,
  hostName = '127.0.0.1',
  mcpCallback: (server: McpServer) => void,
  options?: { mcp?: McpServerOptions },
): Promise<HttpTransportInfo> {
  const transports = new Map<string, StreamableHTTPServerTransport>();
  const sseTransports = new Map<string, SSEServerTransport>();
  const servers: McpServer[] = [];

  const httpServer = createServer(async (req, res) => {
    Logger.log(`HTTP ${req.method} ${req.url}`);

    // Handle SSE connection route
    if (req.url === '/sse') {
      if (req.method === 'GET') {
        await handleSSEConnection(req, res, mcpCallback, sseTransports, servers, options);
        return;
      }
      res.statusCode = 405;
      res.end('Method not allowed for SSE connection endpoint');
      return;
    }

    // Handle SSE message route
    if (req.url?.startsWith('/sse/messages')) {
      if (req.method === 'POST') {
        await handleSSEMessage(req, res, sseTransports);
        return;
      }
      res.statusCode = 405;
      res.end('Method not allowed for SSE message endpoint');
      return;
    }

    // Handle MCP route
    if (req.url === '/mcp') {
      if (req.method === 'POST') {
        await handleStreamableRequest(req, res, mcpCallback, transports, servers, options);
        return;
      }

      if (req.method === 'GET' || req.method === 'DELETE') {
        await handleRequest(req, res, transports);
        return;
      }

      res.statusCode = 400;
      res.end('Invalid request');
      return;
    }

    // Unknown route
    res.statusCode = 404;
    res.end('Not found');
  });

  return new Promise((resolve, reject) => {
    try {
      httpServer.on('error', ex => {
        Logger.error('MCP HTTP server error', ex);
        reject(ex);
      });

      // Let the OS assign an available port by listening on port 0
      httpServer.listen(port, hostName, () => {
        const address = httpServer.address();
        if (address == null || typeof address === 'string') {
          reject(new Error('Failed to get server address'));
          return;
        }

        const serverUrl = `http://${hostName}:${address.port}`;
        Logger.log(`MCP HTTP server listening on ${serverUrl}`);
        resolve({
          httpServer,
          hostName,
          port: address.port,
          url: serverUrl,
          mcpUrl: `${serverUrl}/mcp`,
          sseUrl: `${serverUrl}/sse`,
        });
      });
    } catch (ex) {
      reject(ex as Error);
    }
  });
}

async function handleStreamableRequest(
  req: IncomingMessage,
  res: ServerResponse,
  mcpCallback: (server: McpServer) => void,
  transports: Map<string, StreamableHTTPServerTransport>,
  servers: McpServer[],
  options?: { mcp?: McpServerOptions },
) {
  let bodyString = '';
  req.on('data', chunk => {
    bodyString += chunk.toString();
  });

  req.on('end', async () => {
    let bodyData: any;
    try {
      bodyData = JSON.parse(bodyString);
    } catch (_error) {
      res.statusCode = 400;
      res.end(
        JSON.stringify({
          jsonrpc: '2.0',
          error: { code: -32700, message: 'Parse error: Invalid JSON' },
          id: null,
        }),
      );
      return;
    }

    // Check for existing session ID
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    if (sessionId) {
      const transport = transports.get(sessionId);
      if (!transport) {
        res.statusCode = 400;
        res.end(
          JSON.stringify({
            jsonrpc: '2.0',
            error: { code: -32000, message: 'Bad Request: No valid session ID provided' },
            id: null,
          }),
        );
        return;
      }
      await transport.handleRequest(req, res, bodyData);
      return;
    }

    if (req.method === 'POST' && isInitializeRequest(bodyData)) {
      const transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => uuid(),
        onsessioninitialized: sessionId => {
          transports.set(sessionId, transport);
        },
      });

      transport.onclose = () => {
        if (transport.sessionId) {
          transports.delete(transport.sessionId);
        }
      };

      const server = createMcpServer(options?.mcp?.name, options?.mcp?.version, mcpCallback);
      servers.push(server);

      await server.connect(transport);
      await transport.handleRequest(req, res, bodyData);
      return;
    }

    res.statusCode = 400;
    res.end('Invalid request');
  });
}

async function handleRequest(
  req: IncomingMessage,
  res: ServerResponse,
  transports: Map<string, StreamableHTTPServerTransport>,
) {
  const sessionId = req.headers['mcp-session-id'] as string | undefined;
  if (!sessionId) {
    res.statusCode = 400;
    res.end('Missing session ID');
    return;
  }

  const transport = transports.get(sessionId);
  if (!transport) {
    res.statusCode = 404;
    res.end('Session not found');
    return;
  }

  if (req.method === 'GET') {
    await transport.handleRequest(req, res);
  } else if (req.method === 'DELETE') {
    transports.delete(sessionId);
    res.statusCode = 200;
    res.end('Session closed');
  }
}

async function handleSSEConnection(
  _req: IncomingMessage,
  res: ServerResponse,
  mcpCallback: (server: McpServer) => void,
  sseTransports: Map<string, SSEServerTransport>,
  servers: McpServer[],
  options?: { mcp?: McpServerOptions },
) {
  Logger.log('SSE connection request received');

  const sessionId = uuid();
  const transport = new SSEServerTransport(sessionId, res);

  sseTransports.set(sessionId, transport);

  const server = createMcpServer(options?.mcp?.name, options?.mcp?.version, mcpCallback);
  servers.push(server);

  await server.connect(transport);

  res.on('close', () => {
    Logger.log(`SSE connection closed for session ${sessionId}`);
    sseTransports.delete(sessionId);
  });
}

async function handleSSEMessage(
  req: IncomingMessage,
  res: ServerResponse,
  sseTransports: Map<string, SSEServerTransport>,
) {
  const sessionId = req.headers['mcp-session-id'] as string | undefined;
  if (!sessionId) {
    res.statusCode = 400;
    res.end('Missing session ID');
    return;
  }

  const transport = sseTransports.get(sessionId);
  if (!transport) {
    res.statusCode = 404;
    res.end('Session not found');
    return;
  }

  let bodyString = '';
  req.on('data', chunk => {
    bodyString += chunk.toString();
  });

  req.on('end', async () => {
    try {
      const message = JSON.parse(bodyString);
      await transport.handleMessage(message);
      res.statusCode = 200;
      res.end('OK');
    } catch (error) {
      res.statusCode = 400;
      res.end('Invalid JSON');
    }
  });
}
