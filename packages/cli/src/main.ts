#!/usr/bin/env node

import { Command } from 'commander';
import { StdioCommand } from './commands/stdio';
import { ServeCommand } from './commands/serve';
import { DaemonCommand } from './commands/daemon';
import { StatusCommand } from './commands/status';

const program = new Command();

program.name('wcai-mcp').description('Web Component AI Tools - MCP Server CLI').version('0.0.3');

// STDIO transport command (primary)
program
  .command('stdio')
  .description('Start MCP server with STDIO transport')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-t, --timeout <ms>', 'Request timeout in milliseconds', '5000')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new StdioCommand();
    await command.execute(options);
  });

// HTTP server command
program
  .command('serve')
  .description('Start MCP server with HTTP/SSE transport')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-p, --port <number>', 'HTTP port number', '0')
  .option('-h, --host <address>', 'HTTP host address', '127.0.0.1')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new ServeCommand();
    await command.execute(options);
  });

// Daemon management commands
program
  .command('daemon')
  .description('Start MCP server as background daemon')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-p, --port <number>', 'HTTP port number', '0')
  .option('-h, --host <address>', 'HTTP host address', '127.0.0.1')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new DaemonCommand();
    await command.execute(options);
  });

program
  .command('start')
  .description('Start daemon (alias for daemon command)')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-p, --port <number>', 'HTTP port number', '0')
  .option('-h, --host <address>', 'HTTP host address', '127.0.0.1')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new DaemonCommand();
    await command.execute(options);
  });

program
  .command('stop')
  .description('Stop running daemon')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .action(async options => {
    const command = new StatusCommand();
    await command.stop(options);
  });

program
  .command('status')
  .description('Check daemon status')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .action(async options => {
    const command = new StatusCommand();
    await command.execute(options);
  });

// Parse command line arguments
program.parse();
