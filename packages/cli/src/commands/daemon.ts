import { spawn } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import type { CliOptions, ServerInfo } from '@wcai/shared';
import { Logger } from '../system/logger';

export class DaemonCommand {
  private getPidFilePath(workspace: string): string {
    const hash = Buffer.from(workspace).toString('base64').replace(/[/+=]/g, '');
    return path.join(os.tmpdir(), `wcai-mcp-${hash}.pid`);
  }

  private getLogFilePath(workspace: string): string {
    const hash = Buffer.from(workspace).toString('base64').replace(/[/+=]/g, '');
    return path.join(os.tmpdir(), `wcai-mcp-${hash}.log`);
  }

  async execute(options: CliOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'warn');
      Logger.log('Starting MCP server as daemon');
      Logger.log(`Workspace: ${options.workspace}`);

      const workspace = options.workspace!;
      const pidFile = this.getPidFilePath(workspace);
      const logFile = this.getLogFilePath(workspace);

      // Check if daemon is already running
      try {
        const existingPid = await fs.readFile(pidFile, 'utf-8');
        const pid = parseInt(existingPid.trim());

        // Check if process is still running
        try {
          process.kill(pid, 0); // Signal 0 checks if process exists
          Logger.log(`Daemon already running with PID ${pid}`);
          return;
        } catch {
          // Process not running, remove stale PID file
          await fs.unlink(pidFile).catch(() => {});
        }
      } catch {
        // PID file doesn't exist, continue
      }

      // Spawn daemon process
      const child = spawn(
        process.execPath,
        [
          process.argv[1], // CLI script path
          'serve',
          '--workspace',
          workspace,
          '--port',
          (options.port || 0).toString(),
          '--host',
          options.host || '127.0.0.1',
          ...(options.verbose ? ['--verbose'] : []),
        ],
        {
          detached: true,
          stdio: ['ignore', 'pipe', 'pipe'],
        },
      );

      // Write PID file
      await fs.writeFile(pidFile, child.pid!.toString());

      // Set up log file
      const logStream = await fs.open(logFile, 'w');

      let serverInfo: ServerInfo | undefined;
      let serverInfoReceived = false;

      // Capture server info from stdout
      child.stdout?.on('data', async data => {
        const output = data.toString();
        await logStream.write(output);

        // Try to parse server info JSON
        if (!serverInfoReceived) {
          try {
            const lines = output.split('\n');
            for (const line of lines) {
              if (line.trim().startsWith('{')) {
                const info = JSON.parse(line.trim());
                if (info.transport && info.url) {
                  serverInfo = info;
                  serverInfoReceived = true;
                  break;
                }
              }
            }
          } catch {
            // Not JSON, continue
          }
        }
      });

      // Capture stderr
      child.stderr?.on('data', async data => {
        await logStream.write(`ERROR: ${data.toString()}`);
      });

      // Handle child process events
      child.on('error', async error => {
        Logger.error('Failed to start daemon process', error);
        await logStream.close();
        await fs.unlink(pidFile).catch(() => {});
        process.exit(1);
      });

      child.on('exit', async code => {
        await logStream.close();
        await fs.unlink(pidFile).catch(() => {});
        if (code !== 0) {
          Logger.error(`Daemon process exited with code ${code}`);
          process.exit(1);
        }
      });

      // Detach the child process
      child.unref();

      // Wait for server info or timeout
      const timeout = setTimeout(() => {
        Logger.error('Timeout waiting for daemon to start');
        child.kill();
        process.exit(1);
      }, 10000);

      // Wait for server info
      while (!serverInfoReceived) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      clearTimeout(timeout);

      if (serverInfo) {
        Logger.log(`Daemon started successfully`);
        Logger.log(`PID: ${child.pid}`);
        Logger.log(`Server URL: ${serverInfo.url}`);
        Logger.log(`Log file: ${logFile}`);

        // Output server info for programmatic use
        console.log(JSON.stringify(serverInfo));
      } else {
        Logger.error('Failed to get server info from daemon');
        process.exit(1);
      }
    } catch (error) {
      Logger.error('Failed to start daemon', error);
      process.exit(1);
    }
  }
}
