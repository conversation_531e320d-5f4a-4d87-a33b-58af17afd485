import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import type { CliOptions } from '@wcai/shared';
import { createMcpServer } from '../mcp/server';
import { Logger } from '../system/logger';
import { CemProvider } from '../cem/provider';

export class StdioCommand {
  async execute(options: CliOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'warn');
      Logger.log('Starting MCP server with STDIO transport');
      Logger.log(`Workspace: ${options.workspace}`);

      // Initialize CEM provider
      const cemProvider = new CemProvider(options.workspace!);
      await cemProvider.initialize();

      // Create MCP server with CEM provider
      const server = createMcpServer('wcai-mcp-server', '0.0.3', mcpServer => {
        // Add resources
        mcpServer.resource('manifest://components', 'manifest://components', async () => {
          const components = await cemProvider.getAllComponents();
          return {
            contents: [
              {
                uri: 'manifest://components',
                text: JSON.stringify(components, null, 2),
                mimeType: 'application/json',
              },
            ],
          };
        });

        // Add tools
        mcpServer.tool(
          'search-components',
          'Search for web components by name, tag, or description',
          {
            query: {
              type: 'string',
              description: 'Search query for component name, tag, or description',
            },
            matching: {
              type: 'string',
              enum: ['strict', 'all', 'any'],
              description: 'Matching mode for search',
              default: 'any',
            },
          },
          async args => {
            const { query, matching = 'any' } = args as { query: string; matching?: 'strict' | 'all' | 'any' };
            const components = await cemProvider.searchComponents(query, matching);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(components, null, 2),
                },
              ],
            };
          },
        );

        mcpServer.tool(
          'get-component-details',
          'Get detailed information about a specific component',
          {
            identifier: {
              type: 'string',
              description: 'Component tag name or class name',
            },
            detail: {
              type: 'string',
              enum: ['basic', 'public', 'all'],
              description: 'Level of detail to return',
              default: 'public',
            },
          },
          async args => {
            const { identifier, detail = 'public' } = args as {
              identifier: string;
              detail?: 'basic' | 'public' | 'all';
            };
            const component = await cemProvider.getComponentDetails(identifier, detail);
            return {
              content: [
                {
                  type: 'text',
                  text: component ? JSON.stringify(component, null, 2) : 'Component not found',
                },
              ],
            };
          },
        );

        mcpServer.tool(
          'list-all-components',
          'List all available web components',
          {
            detail: {
              type: 'string',
              enum: ['basic', 'public', 'all'],
              description: 'Level of detail to return',
              default: 'basic',
            },
          },
          async args => {
            const { detail = 'basic' } = args as { detail?: 'basic' | 'public' | 'all' };
            const components = await cemProvider.getAllComponents(detail);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(components, null, 2),
                },
              ],
            };
          },
        );
      });

      // Create STDIO transport
      const transport = new StdioServerTransport();

      // Connect server to transport
      await server.connect(transport);

      Logger.log('MCP server started with STDIO transport');

      // Keep the process running
      process.on('SIGINT', () => {
        Logger.log('Shutting down MCP server');
        process.exit(0);
      });

      process.on('SIGTERM', () => {
        Logger.log('Shutting down MCP server');
        process.exit(0);
      });
    } catch (error) {
      Logger.error('Failed to start STDIO MCP server', error);
      process.exit(1);
    }
  }
}
