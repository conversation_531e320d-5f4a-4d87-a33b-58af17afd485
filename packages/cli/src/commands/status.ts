import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import type { CliOptions, ServerStatus } from '@wcai/shared';
import { Logger } from '../system/logger';

export class StatusCommand {
  private getPidFilePath(workspace: string): string {
    const hash = Buffer.from(workspace).toString('base64').replace(/[/+=]/g, '');
    return path.join(os.tmpdir(), `wcai-mcp-${hash}.pid`);
  }

  private getLogFilePath(workspace: string): string {
    const hash = Buffer.from(workspace).toString('base64').replace(/[/+=]/g, '');
    return path.join(os.tmpdir(), `wcai-mcp-${hash}.log`);
  }

  async execute(options: CliOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'warn');

      const workspace = options.workspace!;
      const pidFile = this.getPidFilePath(workspace);
      const logFile = this.getLogFilePath(workspace);

      let status: ServerStatus = { running: false };

      try {
        // Read PID file
        const pidContent = await fs.readFile(pidFile, 'utf-8');
        const pid = parseInt(pidContent.trim());

        // Check if process is running
        try {
          process.kill(pid, 0); // Signal 0 checks if process exists

          // Process is running, try to get server info from log
          try {
            const logContent = await fs.readFile(logFile, 'utf-8');
            const lines = logContent.split('\n');

            // Find the last server info JSON in the log
            for (let i = lines.length - 1; i >= 0; i--) {
              const line = lines[i].trim();
              if (line.startsWith('{')) {
                try {
                  const serverInfo = JSON.parse(line);
                  if (serverInfo.transport && serverInfo.url) {
                    status = {
                      running: true,
                      pid,
                      serverInfo,
                    };
                    break;
                  }
                } catch {
                  // Not valid JSON, continue
                }
              }
            }

            if (!status.serverInfo) {
              status = { running: true, pid };
            }
          } catch {
            // Can't read log file, but process is running
            status = { running: true, pid };
          }
        } catch {
          // Process not running, clean up PID file
          await fs.unlink(pidFile).catch(() => {});
          status = { running: false };
        }
      } catch {
        // PID file doesn't exist
        status = { running: false };
      }

      // Output status
      console.log(JSON.stringify(status, null, 2));

      if (options.verbose) {
        if (status.running) {
          Logger.log(`Daemon is running (PID: ${status.pid})`);
          if (status.serverInfo) {
            Logger.log(`Server URL: ${status.serverInfo.url}`);
            Logger.log(`Transport: ${status.serverInfo.transport}`);
          }
          Logger.log(`Log file: ${logFile}`);
        } else {
          Logger.log('Daemon is not running');
        }
      }
    } catch (error) {
      Logger.error('Failed to check status', error);
      process.exit(1);
    }
  }

  async stop(options: CliOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'warn');

      const workspace = options.workspace!;
      const pidFile = this.getPidFilePath(workspace);

      try {
        // Read PID file
        const pidContent = await fs.readFile(pidFile, 'utf-8');
        const pid = parseInt(pidContent.trim());

        // Try to kill the process
        try {
          process.kill(pid, 'SIGTERM');

          // Wait for process to exit
          let attempts = 0;
          while (attempts < 50) {
            // Wait up to 5 seconds
            try {
              process.kill(pid, 0);
              await new Promise(resolve => setTimeout(resolve, 100));
              attempts++;
            } catch {
              // Process has exited
              break;
            }
          }

          // Force kill if still running
          try {
            process.kill(pid, 0);
            Logger.log('Process still running, force killing...');
            process.kill(pid, 'SIGKILL');
          } catch {
            // Process has exited
          }

          // Clean up PID file
          await fs.unlink(pidFile).catch(() => {});

          Logger.log(`Daemon stopped (PID: ${pid})`);
        } catch {
          // Process not running
          await fs.unlink(pidFile).catch(() => {});
          Logger.log('Daemon was not running');
        }
      } catch {
        // PID file doesn't exist
        Logger.log('Daemon is not running');
      }
    } catch (error) {
      Logger.error('Failed to stop daemon', error);
      process.exit(1);
    }
  }
}
