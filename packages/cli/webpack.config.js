const path = require('path');

module.exports = {
  target: 'node',
  mode: 'production',
  entry: './src/main.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'main.js',
    clean: true,
  },
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@wcai/shared': path.resolve(__dirname, '../shared/src'),
    },
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  externals: {
    // Keep node modules external for CLI
    '@modelcontextprotocol/sdk': 'commonjs @modelcontextprotocol/sdk',
    '@wc-toolkit/cem-utilities': 'commonjs @wc-toolkit/cem-utilities',
    'custom-elements-manifest': 'commonjs custom-elements-manifest',
    zod: 'commonjs zod',
    commander: 'commonjs commander',
    glob: 'commonjs glob',
    // Bundle shared package
    // '@wcai/shared': 'commonjs @wcai/shared',
  },
  plugins: [],
};
