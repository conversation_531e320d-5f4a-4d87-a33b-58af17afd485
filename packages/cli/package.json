{"name": "@wcai/cli", "version": "0.0.3", "description": "Web Component AI Tools - Standalone MCP CLI Server", "main": "./dist/index.js", "bin": {"wcai-mcp": "./dist/main.js"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "echo \"No tests yet\""}, "dependencies": {"@wcai/shared": "workspace:*", "@modelcontextprotocol/sdk": "catalog:", "@wc-toolkit/cem-utilities": "catalog:", "custom-elements-manifest": "catalog:", "zod": "catalog:", "commander": "catalog:", "glob": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "typescript": "catalog:", "webpack": "catalog:", "webpack-cli": "catalog:", "ts-loader": "catalog:", "eslint": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "rimraf": "catalog:"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN ../../LICENSE", "repository": {"type": "git", "url": "https://github.com/d13/vscode-web-components-ai.git", "directory": "packages/cli"}, "keywords": ["mcp", "model-context-protocol", "web-components", "custom-elements", "ai", "cli"]}