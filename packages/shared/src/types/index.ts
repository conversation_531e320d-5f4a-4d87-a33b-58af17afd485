/**
 * Log levels for the application
 */
export type LogLevel = 'off' | 'error' | 'warn' | 'info' | 'debug';

/**
 * MCP transport types
 */
export type McpTransport = 'stdio' | 'http' | 'sse';

/**
 * Component detail levels for API responses
 */
export type ComponentDetailLevel = 'basic' | 'public' | 'all';

/**
 * Search matching modes for component queries
 */
export type SearchMatchingMode = 'strict' | 'all' | 'any';
