import type { LogLevel, McpTransport, ComponentDetailLevel } from '../types/index';
import type { Server } from 'http';

/**
 * Configuration interface for the application
 */
export interface Config {
  readonly outputLevel: LogLevel;
  readonly manifests: {
    readonly exclude: string[];
  };
  readonly mcp: {
    readonly transport: McpTransport | 'auto';
    readonly port: number | null;
    readonly host: string | null;
    readonly storeHostAndPortOnStart: boolean;
    readonly stdio: {
      readonly timeout: number;
    };
  };
}

/**
 * Server information for MCP server instances
 */
export interface ServerInfo {
  readonly transport: McpTransport;
  readonly url?: string;
  readonly mcpUrl?: string;
  readonly sseUrl?: string;
  readonly port?: number;
  readonly host?: string;
  readonly pid?: number;
  readonly workspace?: string;
}

/**
 * Server status information
 */
export interface ServerStatus {
  readonly running: boolean;
  readonly pid?: number;
  readonly serverInfo?: ServerInfo;
}

/**
 * HTTP transport information
 */
export interface HttpTransportInfo {
  httpServer: Server;
  hostName: string;
  port: number;
  url: string;
  mcpUrl: string;
  sseUrl: string;
}

/**
 * Component information structure
 */
export interface ComponentInfo {
  readonly tagName?: string;
  readonly className?: string;
  readonly description?: string;
  readonly attributes?: Array<{
    name: string;
    description?: string;
    type?: string;
  }>;
  readonly properties?: Array<{
    name: string;
    description?: string;
    type?: string;
  }>;
  readonly methods?: Array<{
    name: string;
    description?: string;
    parameters?: Array<{
      name: string;
      type?: string;
    }>;
  }>;
  readonly events?: Array<{
    name: string;
    description?: string;
    type?: string;
  }>;
}

/**
 * Cache statistics for debugging and monitoring
 */
export interface CacheStats {
  manifestCount: number;
  etag: number | undefined;
  manifestStats: Array<{
    uri: string;
    lastModified: number | undefined;
    cachedComponents: number;
    tagCacheSize: number;
    classCacheSize: number;
    searchCacheSize: number;
  }>;
}

/**
 * CLI command options
 */
export interface CliOptions {
  workspace?: string;
  port?: number;
  host?: string;
  daemon?: boolean;
  timeout?: number;
  verbose?: boolean;
}

/**
 * MCP server options
 */
export interface McpServerOptions {
  name?: string;
  version?: string;
}
