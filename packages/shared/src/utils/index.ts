import type { Component } from '@wc-toolkit/cem-utilities';
import { getComponentPublicMethods, getPropertyOnlyFields } from '@wc-toolkit/cem-utilities';
import type { SearchMatchingMode } from '../types/index';

/**
 * Generate a UUID v4
 */
export function uuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Filter components based on search query and matching mode
 */
export function filterComponents(
  components: Component[],
  query: string,
  matching: SearchMatchingMode = 'any',
): Component[] {
  if (!query.trim()) {
    return components;
  }

  const normalizedQuery = query.toLowerCase();
  if (normalizedQuery.includes(' ')) {
    const words = normalizedQuery.split(/\s+/).filter(word => word.length > 0);
    if (matching === 'all') {
      return components.filter(c => words.every(word => filterComponent(c, word)));
    }
    return components.filter(c => words.some(word => filterComponent(c, word)));
  }

  return components.filter(c => filterComponent(c, normalizedQuery));
}

/**
 * Check if a component matches a search term
 */
function filterComponent(component: Component, term: string): boolean {
  const searchableText = [
    component.tagName,
    component.name,
    component.description,
    ...(component.attributes?.map(a => a.name) || []),
    ...(component.members?.map(m => m.name) || []),
  ]
    .filter(Boolean)
    .join(' ')
    .toLowerCase();

  return searchableText.includes(term);
}

/**
 * Get basic component information
 */
export function getComponentBasicInfo(component: Component) {
  return {
    tagName: component.tagName,
    className: component.name,
    description: component.description,
  };
}

/**
 * Get public API information for a component
 */
export function getComponentPublicApi(component: Component) {
  const publicMethods = getComponentPublicMethods(component);
  const publicProperties = getPropertyOnlyFields(component);

  return {
    tagName: component.tagName,
    className: component.name,
    description: component.description,
    attributes: component.attributes?.map(attr => ({
      name: attr.name,
      description: attr.description,
      type: attr.type?.text,
    })),
    properties: publicProperties?.map(prop => ({
      name: prop.name,
      description: prop.description,
      type: prop.type?.text,
    })),
    methods: publicMethods?.map(method => ({
      name: method.name,
      description: method.description,
      parameters: method.parameters?.map(param => ({
        name: param.name,
        type: param.type?.text,
      })),
    })),
    events: component.events?.map(event => ({
      name: event.name,
      description: event.description,
      type: event.type?.text,
    })),
  };
}

/**
 * Get component details based on detail level
 */
export function getComponentDetails(component: Component, detail: 'basic' | 'public' | 'all' = 'public') {
  switch (detail) {
    case 'basic':
      return getComponentBasicInfo(component);
    case 'public':
      return getComponentPublicApi(component);
    case 'all':
      return component;
  }
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | undefined;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Check if two objects are deeply equal
 */
export function areEqual<T>(a: T, b: T): boolean {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (typeof a !== typeof b) return false;
  if (typeof a !== 'object') return false;

  const keysA = Object.keys(a as any);
  const keysB = Object.keys(b as any);
  if (keysA.length !== keysB.length) return false;

  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!areEqual((a as any)[key], (b as any)[key])) return false;
  }

  return true;
}
