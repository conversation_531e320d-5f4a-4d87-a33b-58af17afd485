{"name": "@wcai/shared", "version": "0.0.3", "description": "Shared types, interfaces, and utilities for Web Component AI Tools", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js", "require": "./dist/types/index.js"}, "./interfaces": {"types": "./dist/interfaces/index.d.ts", "import": "./dist/interfaces/index.js", "require": "./dist/interfaces/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.js", "require": "./dist/utils/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@modelcontextprotocol/sdk": "catalog:", "@wc-toolkit/cem-utilities": "catalog:", "custom-elements-manifest": "catalog:", "zod": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "typescript": "catalog:", "eslint": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "rimraf": "catalog:"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN ../../LICENSE", "repository": {"type": "git", "url": "https://github.com/d13/vscode-web-components-ai.git", "directory": "packages/shared"}}