import { spawn, ChildProcess } from 'child_process';
import { workspace, window } from 'vscode';
import type { ServerInfo, ServerStatus, McpTransport } from '@wcai/shared';
import * as path from 'path';
import * as fs from 'fs/promises';
import { Logger } from './system/logger';

export class CliManager {
  private cliPath: string;
  private stdioProcess?: ChildProcess;
  private httpServerInfo?: ServerInfo;
  private currentTransport: McpTransport | null = null;

  constructor() {
    // Use bundled CLI
    this.cliPath = this.findCliPath();
  }

  private findCliPath(): string {
    // Use the bundled CLI from node_modules
    const bundledPath = path.join(__dirname, '..', 'node_modules', '@wcai', 'cli', 'dist', 'main.js');
    return bundledPath;
  }

  private getPreferredTransport(): McpTransport {
    // For now, default to STDIO. We'll add proper configuration support later
    return 'stdio'; // Prefer STDIO for better performance
  }

  async startServer(preferredTransport?: McpTransport): Promise<ServerInfo | undefined> {
    const workspaceRoot = workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
      window.showErrorMessage('No workspace folder found');
      return;
    }

    const transport = preferredTransport || this.getPreferredTransport();

    try {
      if (transport === 'stdio') {
        return await this.startStdioMode(workspaceRoot);
      } else {
        return await this.startHttpMode(workspaceRoot);
      }
    } catch (error) {
      // Fallback to alternative transport
      if (transport === 'stdio') {
        Logger.warn('STDIO mode failed, falling back to HTTP mode', error);
        return await this.startHttpMode(workspaceRoot);
      } else {
        Logger.warn('HTTP mode failed, falling back to STDIO mode', error);
        return await this.startStdioMode(workspaceRoot);
      }
    }
  }

  private async startStdioMode(workspaceRoot: string): Promise<ServerInfo> {
    Logger.log('Starting MCP server with STDIO transport');

    this.stdioProcess = spawn('node', [this.cliPath, 'stdio', '--workspace', workspaceRoot], {
      stdio: ['pipe', 'pipe', 'pipe'],
    });

    this.currentTransport = 'stdio';

    return {
      transport: 'stdio',
      pid: this.stdioProcess.pid,
      workspace: workspaceRoot,
    };
  }

  private async startHttpMode(workspaceRoot: string): Promise<ServerInfo> {
    Logger.log('Starting MCP server with HTTP transport');

    // Check if daemon is already running
    const status = await this.getServerStatus();
    if (status.running && status.serverInfo) {
      this.httpServerInfo = status.serverInfo;
      this.currentTransport = 'http';
      return status.serverInfo;
    }

    // Start CLI in daemon mode
    const process = spawn(
      'node',
      [
        this.cliPath,
        'daemon',
        '--workspace',
        workspaceRoot,
        '--port',
        '0', // Let OS assign port
      ],
      {
        detached: true,
        stdio: ['ignore', 'pipe', 'pipe'],
      },
    );

    // Capture the server info from stdout
    let serverInfoText = '';
    process.stdout?.on('data', data => {
      serverInfoText += data.toString();
    });

    process.stderr?.on('data', data => {
      Logger.error('CLI Error:', data.toString());
    });

    // Wait for process to output server info
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Server startup timeout'));
      }, 10000);

      process.on('exit', code => {
        clearTimeout(timeout);
        if (code === 0 && serverInfoText) {
          try {
            const serverInfo = JSON.parse(serverInfoText.trim());
            this.httpServerInfo = serverInfo;
            this.currentTransport = 'http';
            resolve(serverInfo);
          } catch (error) {
            reject(new Error(`Failed to parse server info: ${error}`));
          }
        } else {
          reject(new Error(`CLI process exited with code ${code}`));
        }
      });

      process.on('error', error => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  async stopServer(): Promise<void> {
    if (this.currentTransport === 'stdio' && this.stdioProcess) {
      Logger.log('Stopping STDIO MCP server');
      this.stdioProcess.kill('SIGTERM');
      this.stdioProcess = undefined;
      this.currentTransport = null;
      return;
    }

    if (this.currentTransport === 'http') {
      const workspaceRoot = workspace.workspaceFolders?.[0]?.uri.fsPath;
      if (!workspaceRoot) return;

      try {
        const process = spawn('node', [this.cliPath, 'stop', '--workspace', workspaceRoot], {
          stdio: 'ignore',
        });

        await new Promise<void>((resolve, reject) => {
          process.on('exit', code => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Stop command failed with code ${code}`));
            }
          });

          process.on('error', reject);
        });

        this.httpServerInfo = undefined;
        this.currentTransport = null;
      } catch (error) {
        Logger.error('Failed to stop server:', error);
      }
    }
  }

  async sendMcpRequest(request: any): Promise<any> {
    if (this.currentTransport === 'stdio' && this.stdioProcess) {
      return await this.sendStdioRequest(request);
    } else if (this.currentTransport === 'http' && this.httpServerInfo) {
      return await this.sendHttpRequest(request);
    }
    throw new Error('No active MCP transport');
  }

  private async sendStdioRequest(request: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.stdioProcess) {
        reject(new Error('STDIO process not available'));
        return;
      }

      const requestStr = JSON.stringify(request) + '\n';

      // Set up response handler
      const responseHandler = (data: Buffer) => {
        try {
          const response = JSON.parse(data.toString().trim());
          this.stdioProcess?.stdout?.off('data', responseHandler);
          resolve(response);
        } catch (error) {
          reject(error);
        }
      };

      this.stdioProcess.stdout?.on('data', responseHandler);
      this.stdioProcess.stdin?.write(requestStr);

      // Timeout handling
      const timeout = 5000; // 5 seconds timeout for STDIO requests
      setTimeout(() => {
        this.stdioProcess?.stdout?.off('data', responseHandler);
        reject(new Error('STDIO request timeout'));
      }, timeout);
    });
  }

  private async sendHttpRequest(_request: any): Promise<any> {
    // For HTTP requests, we would typically use fetch or similar
    // This is a placeholder for HTTP MCP communication
    throw new Error('HTTP MCP requests not yet implemented');
  }

  async getServerStatus(): Promise<ServerStatus> {
    const workspaceRoot = workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
      return { running: false };
    }

    try {
      const process = spawn('node', [this.cliPath, 'status', '--workspace', workspaceRoot], {
        stdio: ['ignore', 'pipe', 'ignore'],
      });

      let output = '';
      process.stdout?.on('data', data => {
        output += data.toString();
      });

      return new Promise(resolve => {
        process.on('exit', code => {
          if (code === 0 && output) {
            try {
              const status = JSON.parse(output.trim());
              resolve(status);
            } catch {
              resolve({ running: false });
            }
          } else {
            resolve({ running: false });
          }
        });

        process.on('error', () => {
          resolve({ running: false });
        });
      });
    } catch {
      return { running: false };
    }
  }
}
