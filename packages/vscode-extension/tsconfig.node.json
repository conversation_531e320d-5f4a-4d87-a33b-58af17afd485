{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "CommonJS", "target": "ES2020", "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "downlevelIteration": true, "skipLibCheck": true, "rootDir": "./src", "outDir": "./dist", "paths": {"@env/*": ["./src/env/node/*"], "@wcai/shared": ["../shared/src"], "@wcai/cli": ["../cli/src"]}, "tsBuildInfoFile": "tsconfig.node.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["src/test/**/*", "src/env/browser/**/*", "src/webviews/apps/**/*"], "references": [{"path": "../shared"}, {"path": "../cli"}]}