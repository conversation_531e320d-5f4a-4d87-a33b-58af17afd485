{"name": "vscode-wc-ai-tools", "version": "0.0.3", "description": "Web Component AI Tools - Monorepo with MCP CLI and IDE extensions", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN LICENSE", "homepage": "https://github.com/d13/vscode-web-components-ai", "bugs": {"url": "https://github.com/d13/vscode-web-components-ai/issues"}, "repository": {"url": "https://github.com/d13/vscode-web-components-ai.git", "type": "git"}, "engines": {"node": "^20.18.3"}, "private": true, "workspaces": ["packages/*"], "scripts": {"build": "pnpm -r run build", "build:cli": "pnpm --filter @wcai/cli run build", "build:shared": "pnpm --filter @wcai/shared run build", "build:vscode": "pnpm --filter vscode-web-components-ai run build", "clean": "pnpm -r run clean && rimraf dist out .vscode-test .vscode-test-web .eslintcache* tsconfig*.tsbuildinfo", "dev": "pnpm -r run dev", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "pnpm -r run lint", "lint:fix": "pnpm -r run lint:fix", "typecheck": "pnpm -r run typecheck", "test": "pnpm -r run test"}, "devDependencies": {"@eslint/js": "^9.29.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.32.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}, "packageManager": "pnpm@10.10.0"}