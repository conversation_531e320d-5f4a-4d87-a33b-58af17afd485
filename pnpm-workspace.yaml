packages:
  - 'packages/*'

# Catalog for shared dependencies
catalog:
  # Core dependencies
  '@modelcontextprotocol/sdk': '^1.12.3'
  '@wc-toolkit/cem-utilities': '^1.3.0'
  'custom-elements-manifest': '^2.1.0'
  'zod': '^3.25.32'
  
  # CLI dependencies
  'commander': '^12.0.0'
  'glob': '^11.0.0'
  
  # VS Code dependencies
  '@types/vscode': '1.99.0'
  '@vscode/vsce': '^3.5.0'
  'copy-webpack-plugin': '^13.0.0'
  
  # Build tools
  '@types/node': '^22.15.17'
  'typescript': '^5.8.3'
  'ts-loader': '^9.5.2'
  'webpack': '^5.99.9'
  'webpack-cli': '^6.0.1'
  
  # Linting and formatting
  '@eslint/js': '^9.29.0'
  '@typescript-eslint/eslint-plugin': '^8.34.1'
  '@typescript-eslint/parser': '^8.34.1'
  'eslint': '^9.29.0'
  'eslint-config-prettier': '^10.1.5'
  'eslint-import-resolver-typescript': '^4.4.3'
  'eslint-plugin-import': '^2.32.0'
  'prettier': '^3.5.3'
  'typescript-eslint': '^8.34.1'
  
  # Utilities
  'rimraf': '^6.0.1'
  'license-checker-rseidelsohn': '^4.4.2'
