# CLI Usage Guide

The Web Component AI Tools CLI (`wcai-mcp`) is a standalone command-line interface that provides a Model Context Protocol (MCP) server for AI assistants. It can run independently of VS Code and offers multiple transport methods for connecting AI assistants to your web component information.

## Installation

### From Source (Development)

1. Clone the repository and install dependencies:
   ```bash
   git clone https://github.com/d13/vscode-web-components-ai.git
   cd vscode-web-components-ai
   pnpm install
   ```

2. Build the CLI:
   ```bash
   pnpm run build:cli
   ```

3. Run the CLI:
   ```bash
   node packages/cli/dist/main.js --help
   ```

### Global Installation (Future)

> **Note**: Global npm installation will be available in future releases.

## Commands Overview

The CLI provides several commands for different use cases:

- **`stdio`** - Start MCP server with STDIO transport (for direct AI assistant integration)
- **`serve`** - Start MCP server with HTTP/SSE transport (for web-based AI assistants)
- **`daemon`** - Start MCP server as a background daemon
- **`start`** - Alias for `daemon` command
- **`stop`** - Stop a running daemon
- **`status`** - Check daemon status and server information

## Command Reference

### `wcai-mcp stdio`

Starts the MCP server with STDIO transport for direct process communication. This is the primary method for AI assistant integration.

```bash
wcai-mcp stdio [options]
```

**Options:**
- `-w, --workspace <path>` - Workspace directory path (default: current directory)
- `-t, --timeout <ms>` - Request timeout in milliseconds (default: 5000)
- `-v, --verbose` - Enable verbose logging

**Example:**
```bash
# Start STDIO server for current directory
wcai-mcp stdio

# Start with custom workspace and verbose logging
wcai-mcp stdio --workspace /path/to/project --verbose
```

**Use Cases:**
- Direct integration with AI assistants that support STDIO transport
- Development and testing with MCP clients
- Automated scripts and CI/CD pipelines

### `wcai-mcp serve`

Starts the MCP server with HTTP/SSE transport for web-based communication.

```bash
wcai-mcp serve [options]
```

**Options:**
- `-w, --workspace <path>` - Workspace directory path (default: current directory)
- `-p, --port <number>` - HTTP port number (default: 0 for auto-assignment)
- `-h, --host <address>` - HTTP host address (default: 127.0.0.1)
- `-v, --verbose` - Enable verbose logging

**Example:**
```bash
# Start HTTP server on auto-assigned port
wcai-mcp serve

# Start on specific port with verbose logging
wcai-mcp serve --port 3000 --verbose

# Start on custom host and port
wcai-mcp serve --host 0.0.0.0 --port 8080
```

**Use Cases:**
- Web-based AI assistants
- Remote access to component information
- Development servers for testing HTTP endpoints

### `wcai-mcp daemon` / `wcai-mcp start`

Starts the MCP server as a background daemon process. The daemon runs the HTTP server in the background and manages its lifecycle.

```bash
wcai-mcp daemon [options]
wcai-mcp start [options]  # Alias for daemon
```

**Options:**
- `-w, --workspace <path>` - Workspace directory path (default: current directory)
- `-p, --port <number>` - HTTP port number (default: 0 for auto-assignment)
- `-h, --host <address>` - HTTP host address (default: 127.0.0.1)
- `-v, --verbose` - Enable verbose logging

**Example:**
```bash
# Start daemon with auto-assigned port
wcai-mcp daemon

# Start daemon on specific port
wcai-mcp start --port 3001 --verbose
```

**Use Cases:**
- Long-running MCP servers that persist across sessions
- Production deployments
- Multiple AI clients connecting to the same server instance

### `wcai-mcp status`

Checks the status of a running daemon and displays server information.

```bash
wcai-mcp status [options]
```

**Options:**
- `-w, --workspace <path>` - Workspace directory path (default: current directory)
- `-v, --verbose` - Enable verbose logging with additional details

**Example:**
```bash
# Check daemon status
wcai-mcp status

# Check status with verbose output
wcai-mcp status --verbose
```

**Output:**
```json
{
  "running": true,
  "pid": 12345,
  "serverInfo": {
    "transport": "http",
    "url": "http://127.0.0.1:3001",
    "capabilities": {
      "tools": true,
      "resources": true
    }
  }
}
```

### `wcai-mcp stop`

Stops a running daemon process.

```bash
wcai-mcp stop [options]
```

**Options:**
- `-w, --workspace <path>` - Workspace directory path (default: current directory)
- `-v, --verbose` - Enable verbose logging

**Example:**
```bash
# Stop daemon for current workspace
wcai-mcp stop

# Stop daemon with verbose output
wcai-mcp stop --verbose
```

## Usage Patterns

### Development Workflow

1. **Start daemon for development:**
   ```bash
   wcai-mcp daemon --verbose --port 3000
   ```

2. **Check if it's running:**
   ```bash
   wcai-mcp status --verbose
   ```

3. **Test the server:**
   ```bash
   curl http://127.0.0.1:3000/
   ```

4. **Stop when done:**
   ```bash
   wcai-mcp stop
   ```

### AI Assistant Integration

1. **For STDIO-based assistants:**
   ```bash
   wcai-mcp stdio --workspace /path/to/project
   ```

2. **For HTTP-based assistants:**
   ```bash
   wcai-mcp serve --port 3000
   # Then configure your AI assistant to use http://127.0.0.1:3000
   ```

### Production Deployment

1. **Start as daemon:**
   ```bash
   wcai-mcp daemon --workspace /app/workspace --port 8080 --host 0.0.0.0
   ```

2. **Monitor status:**
   ```bash
   wcai-mcp status --workspace /app/workspace
   ```

3. **View logs:**
   ```bash
   # Log file path is shown in status output
   tail -f /tmp/wcai-mcp-*.log
   ```

## Available MCP Tools

When the server is running, it provides these MCP tools to AI assistants:

- **`search-components`** - Search for components by name, tag, or description
- **`get-component-details-by-tag-name`** - Get detailed component information by tag name
- **`get-component-details-by-class-name`** - Get detailed component information by class name  
- **`list-all-components`** - List all available components with optional detail levels

## Available MCP Resources

The server also provides these MCP resources:

- **`manifest://components`** - Access to all component data in JSON format
- **`manifest://components/{tag}`** - Access to specific component data by tag name

## Troubleshooting

### Common Issues

**Port already in use:**
```bash
# Use a different port
wcai-mcp serve --port 3001
```

**Permission denied:**
```bash
# Check if another process is using the port
lsof -i :3000
```

**Daemon won't start:**
```bash
# Check for existing daemon
wcai-mcp status --verbose

# Stop existing daemon if needed
wcai-mcp stop
```

**No components found:**
```bash
# Ensure your workspace has custom-elements.json files
find . -name "custom-elements.json"

# Check package.json for component dependencies
cat package.json | grep -A 10 -B 10 "custom-elements"
```

### Debug Mode

Enable verbose logging for detailed output:
```bash
wcai-mcp [command] --verbose
```

### Log Files

Daemon processes create log files in your system's temp directory:
- **PID file:** `/tmp/wcai-mcp-<hash>.pid`
- **Log file:** `/tmp/wcai-mcp-<hash>.log`

The hash is generated from your workspace path to ensure unique files per workspace.

## Next Steps

- See [AI Assistant Integration Guide](configure-mcp.md) for connecting AI assistants
- See [CLI Contributing Guide](cli-contributing.md) for development information
- Visit [GitHub Discussions](https://github.com/d13/vscode-web-components-ai/discussions) for community support
