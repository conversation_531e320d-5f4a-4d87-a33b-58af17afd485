# CLI Contributing Guide

This guide covers contributing to the Web Component AI Tools CLI (`@wcai/cli` package). The CLI is a standalone Node.js application that provides an MCP server for AI assistants.

## Project Structure

The CLI is located in the `packages/cli` directory and follows this structure:

```
packages/cli/
├── src/
│   ├── commands/          # Command implementations
│   │   ├── daemon.ts      # Background daemon management
│   │   ├── serve.ts       # HTTP/SSE server command
│   │   ├── status.ts      # Status and stop commands
│   │   └── stdio.ts       # STDIO transport command
│   ├── cem/              # Custom Elements Manifest handling
│   ├── mcp/              # MCP server implementation
│   ├── system/           # System utilities (logging, etc.)
│   ├── transports/       # Transport implementations
│   ├── config/           # Configuration management
│   ├── index.ts          # Public API exports
│   └── main.ts           # CLI entry point
├── dist/                 # Compiled JavaScript output
├── package.json          # Package configuration
├── tsconfig.json         # TypeScript configuration
└── webpack.config.js     # Build configuration
```

## Development Setup

### Prerequisites

- Node.js >= 20.18.3
- pnpm >= 10.10.0
- VS Code (recommended)

### Getting Started

1. **Clone and install dependencies:**
   ```bash
   git clone https://github.com/d13/vscode-web-components-ai.git
   cd vscode-web-components-ai
   pnpm install
   ```

2. **Build the CLI:**
   ```bash
   pnpm run build:cli
   ```

3. **Run in development mode:**
   ```bash
   # Watch mode for automatic rebuilds
   pnpm --filter @wcai/cli run dev
   
   # In another terminal, test the CLI
   node packages/cli/dist/main.js --help
   ```

### Development Commands

From the repository root:

```bash
# Build CLI only
pnpm run build:cli

# Watch CLI for changes
pnpm --filter @wcai/cli run dev

# Lint CLI code
pnpm --filter @wcai/cli run lint

# Fix linting issues
pnpm --filter @wcai/cli run lint:fix

# Type check
pnpm --filter @wcai/cli run typecheck

# Clean build artifacts
pnpm --filter @wcai/cli run clean
```

From the CLI package directory (`packages/cli`):

```bash
# Build
pnpm run build

# Watch mode
pnpm run dev

# Lint
pnpm run lint

# Type check
pnpm run typecheck
```

## Architecture Overview

### Command Pattern

The CLI uses the Command pattern with each command implemented as a separate class:

- **`StdioCommand`** - Handles STDIO transport
- **`ServeCommand`** - Handles HTTP/SSE transport
- **`DaemonCommand`** - Manages background daemon processes
- **`StatusCommand`** - Provides status and stop functionality

### Key Components

**Main Entry Point (`main.ts`):**
- Uses Commander.js for CLI argument parsing
- Defines all available commands and options
- Routes commands to appropriate command classes

**MCP Server (`mcp/server.ts`):**
- Creates and configures the MCP server
- Registers tools and resources
- Handles MCP protocol communication

**CEM Provider (`cem/provider.ts`):**
- Discovers and loads Custom Elements Manifests
- Provides component search and retrieval functionality
- Caches manifest data for performance

**Transport Layer (`transports/`):**
- HTTP transport with SSE support
- Integrates with MCP SDK transports

### Dependencies

**Core Dependencies:**
- `@modelcontextprotocol/sdk` - MCP protocol implementation
- `@wc-toolkit/cem-utilities` - Custom Elements Manifest utilities
- `commander` - CLI argument parsing
- `glob` - File pattern matching
- `zod` - Runtime type validation

**Shared Dependencies:**
- `@wcai/shared` - Shared types and utilities across packages

## Debugging

### VS Code Launch Configurations

The repository includes pre-configured launch configurations for debugging:

- **"CLI: stdio"** - Debug STDIO command
- **"CLI: serve"** - Debug HTTP server command
- **"CLI: daemon start"** - Debug daemon startup
- **"CLI: status"** - Debug status command
- **"CLI: stop daemon"** - Debug daemon stop

### Manual Debugging

1. **Build the CLI:**
   ```bash
   pnpm run build:cli
   ```

2. **Run with Node debugger:**
   ```bash
   node --inspect packages/cli/dist/main.js stdio --verbose
   ```

3. **Attach VS Code debugger:**
   - Open VS Code
   - Go to Run and Debug panel
   - Select "Attach to Node Process"
   - Choose the CLI process

### Logging

The CLI uses a custom Logger class with configurable levels:

```typescript
import { Logger } from './system/logger';

// Configure logging level
Logger.configure('debug'); // 'error', 'warn', 'info', 'debug'

// Log messages
Logger.error('Error message', error);
Logger.warn('Warning message');
Logger.info('Info message');
Logger.log('Debug message');
```

## Testing

### Manual Testing

1. **Test STDIO command:**
   ```bash
   node packages/cli/dist/main.js stdio --workspace . --verbose
   ```

2. **Test HTTP server:**
   ```bash
   # Start server
   node packages/cli/dist/main.js serve --port 3000 --verbose
   
   # In another terminal, test endpoints
   curl http://127.0.0.1:3000/
   curl http://127.0.0.1:3000/sse
   ```

3. **Test daemon functionality:**
   ```bash
   # Start daemon
   node packages/cli/dist/main.js daemon --port 3001 --verbose
   
   # Check status
   node packages/cli/dist/main.js status --verbose
   
   # Stop daemon
   node packages/cli/dist/main.js stop --verbose
   ```

### Integration Testing

Test with actual MCP clients:

1. **Create test MCP configuration:**
   ```json
   {
     "mcpServers": {
       "wcai-test": {
         "command": "node",
         "args": ["/path/to/packages/cli/dist/main.js", "stdio", "--workspace", "/test/workspace"]
       }
     }
   }
   ```

2. **Test with MCP client tools or AI assistants**

### Unit Testing

> **Note**: Unit tests are not yet implemented. This is a great area for contribution!

Future testing framework setup:
- Jest or Vitest for test runner
- Mock MCP clients for integration tests
- Test fixtures for Custom Elements Manifests

## Common Development Tasks

### Adding a New Command

1. **Create command class:**
   ```typescript
   // packages/cli/src/commands/my-command.ts
   import type { CliOptions } from '@wcai/shared';
   import { Logger } from '../system/logger';
   
   export class MyCommand {
     async execute(options: CliOptions): Promise<void> {
       Logger.configure(options.verbose ? 'debug' : 'warn');
       // Implementation here
     }
   }
   ```

2. **Register in main.ts:**
   ```typescript
   import { MyCommand } from './commands/my-command';
   
   program
     .command('my-command')
     .description('Description of my command')
     .option('-v, --verbose', 'Enable verbose logging')
     .action(async options => {
       const command = new MyCommand();
       await command.execute(options);
     });
   ```

3. **Add launch configuration (optional):**
   ```json
   {
     "name": "CLI: my-command",
     "type": "node",
     "request": "launch",
     "program": "${workspaceFolder}/packages/cli/dist/main.js",
     "args": ["my-command", "--verbose"],
     "preLaunchTask": "build:cli"
   }
   ```

### Adding MCP Tools

1. **Implement tool in server setup:**
   ```typescript
   // In mcp/server.ts or command files
   mcpServer.tool(
     'my-tool',
     'Description of my tool',
     {
       param1: {
         type: 'string',
         description: 'Parameter description'
       }
     },
     async (args) => {
       // Tool implementation
       return {
         content: [{
           type: 'text',
           text: 'Tool result'
         }]
       };
     }
   );
   ```

### Modifying Transport Layer

1. **HTTP transport modifications:**
   - Edit `transports/http.ts`
   - Add new endpoints or modify existing ones
   - Update SSE handling if needed

2. **STDIO transport modifications:**
   - Usually handled by MCP SDK
   - Modify command classes if needed

## Code Style and Standards

### TypeScript Guidelines

- Use strict TypeScript configuration
- Prefer explicit types over `any`
- Use proper error handling with try/catch
- Follow existing naming conventions

### Code Organization

- Keep command classes focused and single-purpose
- Use dependency injection where appropriate
- Separate concerns (transport, business logic, etc.)
- Add proper JSDoc comments for public APIs

### Error Handling

```typescript
try {
  // Operation that might fail
} catch (error) {
  Logger.error('Descriptive error message', error);
  process.exit(1); // For CLI commands
}
```

## Building and Distribution

### Build Process

The CLI uses TypeScript compiler (not webpack) for building:

```bash
# Development build
pnpm run build:cli

# Watch mode
pnpm --filter @wcai/cli run dev
```

### Binary Distribution

The CLI is distributed as:
- **Source**: TypeScript files in `src/`
- **Compiled**: JavaScript files in `dist/`
- **Entry point**: `dist/main.js` (specified in package.json `bin` field)

### Future Packaging

Plans for distribution:
- npm package for global installation
- Standalone binaries using pkg or similar
- Docker images for containerized deployment

## Contributing Guidelines

### Pull Request Process

1. **Fork and create feature branch:**
   ```bash
   git checkout -b feature/my-cli-feature
   ```

2. **Make changes and test:**
   ```bash
   pnpm run build:cli
   # Test your changes
   ```

3. **Lint and type check:**
   ```bash
   pnpm --filter @wcai/cli run lint
   pnpm --filter @wcai/cli run typecheck
   ```

4. **Create pull request with:**
   - Clear description of changes
   - Test instructions
   - Any breaking changes noted

### Areas for Contribution

**High Priority:**
- Unit and integration tests
- Error handling improvements
- Performance optimizations
- Documentation improvements

**Medium Priority:**
- Additional MCP tools
- Configuration file support
- Logging improvements
- CLI help text enhancements

**Future Features:**
- Plugin system
- Custom transport implementations
- Advanced caching strategies
- Metrics and monitoring

## Getting Help

- **GitHub Issues**: Report bugs and request features
- **GitHub Discussions**: Ask questions and share ideas
- **Code Review**: Submit PRs for feedback
- **Documentation**: Improve guides and examples

## Related Documentation

- [CLI Usage Guide](cli-usage.md) - End-user documentation
- [MCP Configuration Guide](configure-mcp.md) - AI assistant integration
- [Main Contributing Guide](../CONTRIBUTING.md) - General project contribution guidelines
