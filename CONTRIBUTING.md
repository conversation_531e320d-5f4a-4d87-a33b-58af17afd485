# Contributing

Thank you for your interest in contributing to this project!

## Following extension guidelines

Please note the [Code of Conduct](CODE_OF_CONDUCT.md) document, please follow it in all your interactions with this project.

Ensure that you've read through the extensions guidelines and follow the best practices for creating your extension.

- [Extension Guidelines](https://code.visualstudio.com/api/references/extension-guidelines)

## Getting Started

### Prerequisites

- [NodeJS](https://nodejs.org/), `>= 20.18.3`
- [Corepack](https://nodejs.org/docs/latest-v22.x/api/corepack.html), `>= 0.31.0`
- [pnpm](https://pnpm.io/), `>= 10.10.0` (installs using corepack)

For those using [nvm](https://github.com/nvm-sh/nvm), simply run the following command to install the required version of NodeJS:

```
nvm use
```

> 👉 **NOTE!** Corepack version
>
> Check your version of corepack by running `corepack -v` and ensure it is at least `0.31.0`. To update corepack, run `npm install corepack@latest`. You can enable corepack by running `corepack enable`.

### Dependencies

To install the dependencies for this project, run the following command in the root directory:

```bash
pnpm install
```

### Build

From a terminal, where you have cloned the repository, execute the following command to build the project:

```bash
pnpm run build
```

### Watch

During development you can use a watcher to make builds on changes quick and easy. From a terminal, where you have cloned the repository, execute the following command:

```bash
pnpm run watch
```

## Project Structure

This project uses pnpm workspaces with the following packages:

- **`packages/vscode-extension`** - VS Code extension
- **`packages/cli`** - Standalone CLI for MCP server
- **`packages/shared`** - Shared types and utilities

## Package-Specific Development

### VS Code Extension

To develop the VS Code extension:

```bash
# Build extension
pnpm run build:vscode

# Watch mode
pnpm --filter vscode-web-components-ai run watch

# Run in VS Code
# Use "Watch & Run Extension" launch configuration
```

### CLI Development

To develop the CLI:

```bash
# Build CLI
pnpm run build:cli

# Watch mode
pnpm --filter @wcai/cli run dev

# Test CLI
node packages/cli/dist/main.js --help
```

See the [CLI Contributing Guide](docs/cli-contributing.md) for detailed CLI development information.

### Shared Package

To develop shared utilities:

```bash
# Build shared package
pnpm run build:shared

# Watch mode
pnpm --filter @wcai/shared run dev
```

## Running Locally

### VS Code Extension

To run the extension locally, you can use the launch configurations in the Run and Debug sidebar:

- **"Run Extension"** - Run extension without watch mode
- **"Watch & Run Extension"** - Run extension with automatic rebuilds

### CLI Testing

Use the CLI launch configurations for debugging:

- **"CLI: stdio"** - Test STDIO transport
- **"CLI: serve"** - Test HTTP server
- **"CLI: daemon start"** - Test daemon functionality

## Testing

### Manual Testing

Test the extension and CLI with real web component projects:

1. **Create test workspace** with `custom-elements.json` files
2. **Install component libraries** in package.json dependencies
3. **Test MCP server** with AI assistants
4. **Verify component discovery** and tool functionality

### Integration Testing

Test with actual AI assistants:

1. **Configure MCP client** (see [MCP Configuration Guide](docs/configure-mcp.md))
2. **Test MCP tools** (search-components, get-component-details, etc.)
3. **Verify component data** accuracy and completeness
