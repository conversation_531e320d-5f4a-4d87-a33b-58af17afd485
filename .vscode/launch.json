// A launch configuration that compiles the extension and then opens it inside a new window
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Extension",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": ["--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-extension"],
      "outFiles": ["${workspaceFolder}/packages/vscode-extension/dist/**/*.js"],
      "presentation": {
        "group": "2_extension",
        "order": 1
      },
      "pauseForSourceMap": true,
      "skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
      "smartStep": true,
      "sourceMapRenames": true,
      "sourceMaps": true
    },
    {
      "name": "Watch & Run Extension",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": ["--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-extension"],
      "outFiles": ["${workspaceFolder}/packages/vscode-extension/dist/**/*.js"],
      "pauseForSourceMap": true,
      "preLaunchTask": "${defaultBuildTask}",
      "presentation": {
        "group": "1_extension",
        "order": 1
      },
      "skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
      "smartStep": true,
      "sourceMapRenames": true,
      "sourceMaps": true
    },
    {
      "name": "CLI: stdio",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/packages/cli/dist/main.js",
      "args": ["stdio", "--verbose", "--workspace", "${workspaceFolder}"],
      "cwd": "${workspaceFolder}",
      "outFiles": ["${workspaceFolder}/packages/cli/dist/**/*.js"],
      "presentation": {
        "group": "3_cli",
        "order": 1
      },
      "preLaunchTask": "build:cli",
      "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
      "smartStep": true,
      "sourceMapRenames": true,
      "sourceMaps": true,
      "console": "integratedTerminal"
    },
    {
      "name": "CLI: serve",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/packages/cli/dist/main.js",
      "args": ["serve", "--verbose", "--workspace", "${workspaceFolder}", "--port", "3000"],
      "cwd": "${workspaceFolder}",
      "outFiles": ["${workspaceFolder}/packages/cli/dist/**/*.js"],
      "presentation": {
        "group": "3_cli",
        "order": 2
      },
      "preLaunchTask": "build:cli",
      "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
      "smartStep": true,
      "sourceMapRenames": true,
      "sourceMaps": true,
      "console": "integratedTerminal"
    },
    {
      "name": "CLI: status",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/packages/cli/dist/main.js",
      "args": ["status", "--workspace", "${workspaceFolder}"],
      "cwd": "${workspaceFolder}",
      "outFiles": ["${workspaceFolder}/packages/cli/dist/**/*.js"],
      "presentation": {
        "group": "3_cli",
        "order": 3
      },
      "preLaunchTask": "build:cli",
      "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
      "smartStep": true,
      "sourceMapRenames": true,
      "sourceMaps": true,
      "console": "integratedTerminal"
    }
  ]
}
