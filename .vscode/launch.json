// A launch configuration that compiles the extension and then opens it inside a new window
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": ["--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-extension"],
      "outFiles": ["${workspaceFolder}/packages/vscode-extension/dist/**/*.js"],
      "presentation": {
        "group": "2_run",
        "order": 1
      },
      "pauseForSourceMap": true,
      "skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
      "smartStep": true,
      "sourceMapRenames": true,
      "sourceMaps": true
    },
    {
      "name": "Watch & Run",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": ["--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-extension"],
      "outFiles": ["${workspaceFolder}/packages/vscode-extension/dist/**/*.js"],
      "pauseForSourceMap": true,
      "preLaunchTask": "${defaultBuildTask}",
      "presentation": {
        "group": "1_watch",
        "order": 1
      },
      "skipFiles": ["<node_internals>/**", "**/node_modules/**", "**/resources/app/out/vs/**"],
      "smartStep": true,
      "sourceMapRenames": true,
      "sourceMaps": true
    }
  ]
}
